
	// 导入表单组件
	import FormInput from './components/form-input.uvue'
	import FormTextarea from './components/form-textarea.uvue'
	import FormSwitch from './components/form-switch.uvue'
	import FormSlider from './components/form-slider.uvue'
	import FormNumberbox from './components/form-numberbox.uvue'
	import { FormFieldData ,FormChangeEvent} from '@/components/main-form/form_type.uts'


	// 字段变更事件类型
	type FieldChangeEvent = { __$originalPosition?: UTSSourceMapPosition<"FieldChangeEvent", "components/main-form/main-form.uvue", 32, 7>;
		index : number;
		value : any
	}


	const __sfc__ = defineComponent({
		name: "main-form",
		components: {
			FormInput,
			FormTextarea,
			FormSwitch,
			FormSlider,
			FormNumberbox
		},
		props: {
			// 表单数据数组
			formData: {
				type: Array as PropType<FormFieldData[]>,
				default: () : Array<FormFieldData> => []
			},
			// 表单标题
			title: {
				type: String,
				default: ""
			},

			// 存储键名前缀
			keyName: {
				type: String,
				default: ""
			}
		},
		computed: {
		
		},
		methods: {
			change(event:FormChangeEvent){

				// 获取当前字段索引
				const fieldIndex = event.index as number
				const fieldValue=event.value as any
				this.formData[fieldIndex].value=fieldValue 
				
			}
		}
	})

export default __sfc__
function GenComponentsMainFormMainFormRender(this: InstanceType<typeof __sfc__>): any | null {
const _ctx = this
const _cache = this.$.renderCache
const _component_FormInput = resolveComponent("FormInput")
const _component_FormTextarea = resolveComponent("FormTextarea")
const _component_FormSwitch = resolveComponent("FormSwitch")
const _component_FormSlider = resolveComponent("FormSlider")
const _component_FormNumberbox = resolveComponent("FormNumberbox")

  return _cE("view", _uM({ class: "main-form" }), [
    _ctx.title != ''
      ? _cE("view", _uM({
          key: 0,
          class: "main-form-title"
        }), [
          _cE("text", null, _tD(_ctx.title), 1 /* TEXT */)
        ])
      : _cC("v-if", true),
    _cE("view", _uM({ class: "main-form-content" }), [
      _cE(Fragment, null, RenderHelpers.renderList(_ctx.formData, (item, index, __index, _cached): any => {
        return _cE("view", _uM({ class: "main-form-item" }), [
          item.type=='input'
            ? _cV(_component_FormInput, _uM({
                key: 0,
                data: item,
                index: index,
                keyName: _ctx.keyName,
                onChange: _ctx.change
              }), null, 8 /* PROPS */, ["data", "index", "keyName", "onChange"])
            : _cC("v-if", true),
          item.type=='textarea'
            ? _cV(_component_FormTextarea, _uM({
                key: 1,
                data: item,
                index: index,
                keyName: _ctx.keyName,
                onChange: _ctx.change
              }), null, 8 /* PROPS */, ["data", "index", "keyName", "onChange"])
            : _cC("v-if", true),
          item.type=='switch'
            ? _cV(_component_FormSwitch, _uM({
                key: 2,
                data: item,
                index: index,
                keyName: _ctx.keyName,
                onChange: _ctx.change
              }), null, 8 /* PROPS */, ["data", "index", "keyName", "onChange"])
            : _cC("v-if", true),
          item.type=='slider'
            ? _cV(_component_FormSlider, _uM({
                key: 3,
                data: item,
                index: index,
                keyName: _ctx.keyName,
                onChange: _ctx.change
              }), null, 8 /* PROPS */, ["data", "index", "keyName", "onChange"])
            : _cC("v-if", true),
          item.type=='numberbox'
            ? _cV(_component_FormNumberbox, _uM({
                key: 4,
                data: item,
                index: index,
                keyName: _ctx.keyName,
                onChange: _ctx.change
              }), null, 8 /* PROPS */, ["data", "index", "keyName", "onChange"])
            : _cC("v-if", true)
        ])
      }), 256 /* UNKEYED_FRAGMENT */)
    ])
  ])
}
export type MainFormComponentPublicInstance = InstanceType<typeof __sfc__>;
const GenComponentsMainFormMainFormStyles = [_uM([["main-form", _pS(_uM([["width", "100%"], ["display", "flex"], ["flexDirection", "column"]]))], ["main-form-title", _pS(_uM([["width", "710rpx"], ["marginTop", 0], ["marginRight", "auto"], ["marginBottom", "20rpx"], ["marginLeft", "auto"], ["paddingTop", 0], ["paddingRight", "20rpx"], ["paddingBottom", 0], ["paddingLeft", "20rpx"], ["borderLeftWidth", "10rpx"], ["borderLeftStyle", "solid"], ["borderLeftColor", "#1F6ED4"], ["display", "flex"], ["alignItems", "center"]]))], ["main-form-content", _pS(_uM([["width", "100%"], ["display", "flex"], ["flexDirection", "column"]]))], ["main-form-item", _pS(_uM([["width", "100%"]]))]])]
