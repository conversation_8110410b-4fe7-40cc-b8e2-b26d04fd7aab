{"version": 3, "sources": ["components/main-form/main-form.uvue"], "names": [], "mappings": "AAqBC,SAAQ;AACR,OAAO,SAAQ,MAAO,8BAA6B,CAAA;AACnD,OAAO,YAAW,MAAO,iCAAgC,CAAA;AACzD,OAAO,UAAS,MAAO,+BAA8B,CAAA;AACrD,OAAO,UAAS,MAAO,+BAA8B,CAAA;AACrD,OAAO,aAAY,MAAO,kCAAiC,CAAA;AAC3D,OAAO,EAAE,aAAY,EAAG,eAAe,EAAA,MAAO,sCAAqC,CAAA;AAGnF,WAAU;AACV,KAAK,gBAAe,GAAI;IAAA,mBAAA,CAAA,EAAA,oBAAA,CAAA,kBAAA,EAAA,qCAAA,EAAA,EAAA,EAAA,CAAA,CAAA,CAAA;IACvB,KAAI,EAAI,MAAM,CAAA;IACd,KAAI,EAAI,GAAE,CAAA;CACX,CAAA;AAGA,MAAK,OAAQ,GAAE,eAAA,CAAA;IACd,IAAI,EAAE,WAAW;IACjB,UAAU,EAAE;QACX,SAAS;QACT,YAAY;QACZ,UAAU;QACV,UAAU;QACV,aAAY;KACZ;IACD,KAAK,EAAE;QACN,SAAQ;QACR,QAAQ,EAAE;YACT,IAAI,EAAE,KAAI,IAAK,QAAQ,CAAC,aAAa,EAAE,CAAC;YACxC,OAAO,EAAE,IAAK,KAAK,CAAC,aAAa,CAAA,CAAE,EAAC,CAAE,EAAC;SACvC;QACD,OAAM;QACN,KAAK,EAAE;YACN,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,EAAC;SACV;QAED,SAAQ;QACR,OAAO,EAAE;YACR,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,EAAC;SACX;KACA;IACD,QAAQ,EAAE,EAET;IACD,OAAO,EAAE;QACR,MAAM,CAAC,KAAK,EAAC,eAAe;YAE3B,WAAU;YACV,MAAM,UAAS,GAAI,KAAK,CAAC,KAAI,IAAK,MAAK,CAAA;YACvC,MAAM,UAAU,GAAC,KAAK,CAAC,KAAI,IAAK,GAAE,CAAA;YAClC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,KAAK,GAAC,UAAS,CAAA;QAE1C,CAAA;KACD;CACD,CAAA,CAAA;;;;;;;;;;WA5EA,GAAA,CAgBO,MAAA,EAAA,GAAA,CAAA,EAhBD,KAAK,EAAC,WAAW,EAAA,CAAA,EAAA;QAEc,IAAA,CAAA,KAAK,IAAA,EAAA;cAAzC,GAAA,CAEO,MAAA,EAAA,GAAA,CAAA;;gBAFD,KAAK,EAAC,iBAAiB;;gBAC5B,GAAA,CAAyB,MAAA,EAAA,IAAA,EAAA,GAAA,CAAf,IAAA,CAAA,KAAK,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;;;QAIhB,GAAA,CAQO,MAAA,EAAA,GAAA,CAAA,EARD,KAAK,EAAC,mBAAmB,EAAA,CAAA,EAAA;YAC9B,GAAA,CAMO,QAAA,EAAA,IAAA,EAAA,aAAA,CAAA,UAAA,CAN8C,IAAA,CAAA,QAAQ,EAAA,CAAxB,IAAI,EAAE,KAAK,EAAX,OAAI,EAAA,OAAA,GAAA,GAAA,CAAA,EAAA;uBAAzC,GAAA,CAMO,MAAA,EAAA,GAAA,CAAA,EAND,KAAK,EAAC,gBAAgB,EAAA,CAAA,EAAA;oBACV,IAAI,CAAC,IAAI,IAAA,OAAA;0BAA1B,GAAA,CAAiH,oBAAA,EAAA,GAAA,CAAA;;4BAA3E,IAAI,EAAE,IAAI;4BAAG,KAAK,EAAE,KAAK;4BAAG,OAAO,EAAE,IAAA,CAAA,OAAO;4BAAG,QAAM,EAAE,IAAA,CAAA,MAAM;;;oBAC/E,IAAI,CAAC,IAAI,IAAA,UAAA;0BAA7B,GAAA,CAA0H,uBAAA,EAAA,GAAA,CAAA;;4BAA9E,IAAI,EAAE,IAAI;4BAAG,KAAK,EAAE,KAAK;4BAAG,OAAO,EAAE,IAAA,CAAA,OAAO;4BAAG,QAAM,EAAE,IAAA,CAAA,MAAM;;;oBACvF,IAAI,CAAC,IAAI,IAAA,QAAA;0BAA3B,GAAA,CAAoH,qBAAA,EAAA,GAAA,CAAA;;4BAA5E,IAAI,EAAE,IAAI;4BAAG,KAAK,EAAE,KAAK;4BAAG,OAAO,EAAE,IAAA,CAAA,OAAO;4BAAG,QAAM,EAAE,IAAA,CAAA,MAAM;;;oBACnF,IAAI,CAAC,IAAI,IAAA,QAAA;0BAA3B,GAAA,CAAoH,qBAAA,EAAA,GAAA,CAAA;;4BAA5E,IAAI,EAAE,IAAI;4BAAG,KAAK,EAAE,KAAK;4BAAG,OAAO,EAAE,IAAA,CAAA,OAAO;4BAAG,QAAM,EAAE,IAAA,CAAA,MAAM;;;oBAChF,IAAI,CAAC,IAAI,IAAA,WAAA;0BAA9B,GAAA,CAA6H,wBAAA,EAAA,GAAA,CAAA;;4BAA/E,IAAI,EAAE,IAAI;4BAAG,KAAK,EAAE,KAAK;4BAAG,OAAO,EAAE,IAAA,CAAA,OAAO;4BAAG,QAAM,EAAE,IAAA,CAAA,MAAM", "file": "components/main-form/main-form.uvue", "sourcesContent": ["<template>\r\n\t<view class=\"main-form\" >\r\n\t\t<!-- 表单标题 -->\r\n\t\t<view class=\"main-form-title\" v-if=\"title != ''\" >\r\n\t\t\t<text >{{ title }}</text>\r\n\t\t</view>\r\n\r\n\t\t<!-- 表单项容器 -->\r\n\t\t<view class=\"main-form-content\">\r\n\t\t\t<view class=\"main-form-item\" v-for=\"(item, index) in formData\">\r\n\t\t\t\t<FormInput v-if=\"item.type=='input'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\"></FormInput>\r\n\t\t\t\t<FormTextarea v-if=\"item.type=='textarea'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\"></FormTextarea>\r\n\t\t\t\t<FormSwitch v-if=\"item.type=='switch'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\"></FormSwitch>\r\n\t\t\t\t<FormSlider v-if=\"item.type=='slider'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\"></FormSlider>\r\n\t\t\t\t<FormNumberbox v-if=\"item.type=='numberbox'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\"></FormNumberbox>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script lang=\"uts\">\r\n\t// 导入表单组件\r\n\timport FormInput from './components/form-input.uvue'\r\n\timport FormTextarea from './components/form-textarea.uvue'\r\n\timport FormSwitch from './components/form-switch.uvue'\r\n\timport FormSlider from './components/form-slider.uvue'\r\n\timport FormNumberbox from './components/form-numberbox.uvue'\r\n\timport { FormFieldData ,FormChangeEvent} from '@/components/main-form/form_type.uts'\r\n\r\n\r\n\t// 字段变更事件类型\r\n\ttype FieldChangeEvent = {\r\n\t\tindex : number;\r\n\t\tvalue : any\r\n\t}\r\n\r\n\r\n\texport default {\r\n\t\tname: \"main-form\",\r\n\t\tcomponents: {\r\n\t\t\tFormInput,\r\n\t\t\tFormTextarea,\r\n\t\t\tFormSwitch,\r\n\t\t\tFormSlider,\r\n\t\t\tFormNumberbox\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\t// 表单数据数组\r\n\t\t\tformData: {\r\n\t\t\t\ttype: Array as PropType<FormFieldData[]>,\r\n\t\t\t\tdefault: () : Array<FormFieldData> => []\r\n\t\t\t},\r\n\t\t\t// 表单标题\r\n\t\t\ttitle: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"\"\r\n\t\t\t},\r\n\r\n\t\t\t// 存储键名前缀\r\n\t\t\tkeyName: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"\"\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tchange(event:FormChangeEvent){\r\n\r\n\t\t\t\t// 获取当前字段索引\r\n\t\t\t\tconst fieldIndex = event.index as number\r\n\t\t\t\tconst fieldValue=event.value as any\r\n\t\t\t\tthis.formData[fieldIndex].value=fieldValue \r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.main-form {\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.main-form-title {\r\n\t\twidth: 710rpx;\r\n\t\tmargin: 0 auto;\r\n\t\tpadding: 0 20rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tborder-left: 10rpx solid #1F6ED4;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.main-form-content {\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.main-form-item {\r\n\t\twidth: 100%;\r\n\t}\r\n</style>"]}