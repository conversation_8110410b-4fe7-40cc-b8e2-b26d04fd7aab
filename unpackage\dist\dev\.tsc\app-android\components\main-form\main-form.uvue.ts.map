{"version": 3, "file": "components/main-form/main-form.uvue", "names": [], "sources": ["components/main-form/main-form.uvue"], "sourcesContent": ["<template>\r\n\t<view class=\"main-form\" >\r\n\t\t<!-- 表单标题 -->\r\n\t\t<view class=\"main-form-title\" v-if=\"title != ''\" >\r\n\t\t\t<text >{{ title }}</text>\r\n\t\t</view>\r\n\r\n\t\t<!-- 表单项容器 -->\r\n\t\t<view class=\"main-form-content\">\r\n\t\t\t<view class=\"main-form-item\" v-for=\"(item, index) in formData\">\r\n\t\t\t\t<FormInput v-if=\"item.type=='input'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\"></FormInput>\r\n\t\t\t\t<FormTextarea v-if=\"item.type=='textarea'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\"></FormTextarea>\r\n\t\t\t\t<FormSwitch v-if=\"item.type=='switch'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\"></FormSwitch>\r\n\t\t\t\t<FormSlider v-if=\"item.type=='slider'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\"></FormSlider>\r\n\t\t\t\t<FormNumberbox v-if=\"item.type=='numberbox'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\"></FormNumberbox>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script lang=\"uts\">\r\n\t// 导入表单组件\r\n\timport FormInput from './components/form-input.uvue'\r\n\timport FormTextarea from './components/form-textarea.uvue'\r\n\timport FormSwitch from './components/form-switch.uvue'\r\n\timport FormSlider from './components/form-slider.uvue'\r\n\timport FormNumberbox from './components/form-numberbox.uvue'\r\n\timport { FormFieldData ,FormChangeEvent} from '@/components/main-form/form_type.uts'\r\n\r\n\r\n\t// 字段变更事件类型\r\n\ttype FieldChangeEvent = {\r\n\t\tindex : number;\r\n\t\tvalue : any\r\n\t}\r\n\r\n\r\n\texport default {\r\n\t\tname: \"main-form\",\r\n\t\tcomponents: {\r\n\t\t\tFormInput,\r\n\t\t\tFormTextarea,\r\n\t\t\tFormSwitch,\r\n\t\t\tFormSlider,\r\n\t\t\tFormNumberbox\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\t// 表单数据数组\r\n\t\t\tformData: {\r\n\t\t\t\ttype: Array as PropType<FormFieldData[]>,\r\n\t\t\t\tdefault: () : Array<FormFieldData> => []\r\n\t\t\t},\r\n\t\t\t// 表单标题\r\n\t\t\ttitle: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"\"\r\n\t\t\t},\r\n\r\n\t\t\t// 存储键名前缀\r\n\t\t\tkeyName: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"\"\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tchange(event:FormChangeEvent){\r\n\r\n\t\t\t\t// 获取当前字段索引\r\n\t\t\t\tconst fieldIndex = event.index as number\r\n\t\t\t\tconst fieldValue=event.value as any\r\n\t\t\t\tthis.formData[fieldIndex].value=fieldValue \r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.main-form {\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.main-form-title {\r\n\t\twidth: 710rpx;\r\n\t\tmargin: 0 auto;\r\n\t\tpadding: 0 20rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tborder-left: 10rpx solid #1F6ED4;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.main-form-content {\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.main-form-item {\r\n\t\twidth: 100%;\r\n\t}\r\n</style>"], "mappings": ";CAqBC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;CACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACzD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACrD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACrD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;CAGnF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;EACvB,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;CACX;;;CAGA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACN,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;GACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;GACxC,CAAC;GACD,CAAC,EAAE,CAAC,CAAC,CAAC;GACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;GACX,CAAC;;GAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;GACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;GACX;EACD,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;EAEV,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE5B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;GAE1C;EACD;CACD;;;;;;;;;;;;SA5EA,IAgBO,cAhBD,KAAK,EAAC,WAAW;IAEc,UAAK;QAAzC,IAEO;;UAFD,KAAK,EAAC,iBAAiB;;UAC5B,IAAyB,kBAAf,UAAK;;;IAIhB,IAQO,cARD,KAAK,EAAC,mBAAmB;MAC9B,IAMO,yCAN8C,aAAQ,GAAxB,IAAI,EAAE,KAAK,EAAX,OAAI;eAAzC,IAMO,cAND,KAAK,EAAC,gBAAgB;UACV,IAAI,CAAC,IAAI;cAA1B,IAAiH;;gBAA3E,IAAI,EAAE,IAAI;gBAAG,KAAK,EAAE,KAAK;gBAAG,OAAO,EAAE,YAAO;gBAAG,QAAM,EAAE,WAAM;;;UAC/E,IAAI,CAAC,IAAI;cAA7B,IAA0H;;gBAA9E,IAAI,EAAE,IAAI;gBAAG,KAAK,EAAE,KAAK;gBAAG,OAAO,EAAE,YAAO;gBAAG,QAAM,EAAE,WAAM;;;UACvF,IAAI,CAAC,IAAI;cAA3B,IAAoH;;gBAA5E,IAAI,EAAE,IAAI;gBAAG,KAAK,EAAE,KAAK;gBAAG,OAAO,EAAE,YAAO;gBAAG,QAAM,EAAE,WAAM;;;UACnF,IAAI,CAAC,IAAI;cAA3B,IAAoH;;gBAA5E,IAAI,EAAE,IAAI;gBAAG,KAAK,EAAE,KAAK;gBAAG,OAAO,EAAE,YAAO;gBAAG,QAAM,EAAE,WAAM;;;UAChF,IAAI,CAAC,IAAI;cAA9B,IAA6H;;gBAA/E,IAAI,EAAE,IAAI;gBAAG,KAAK,EAAE,KAAK;gBAAG,OAAO,EAAE,YAAO;gBAAG,QAAM,EAAE,WAAM"}