{"version": 3, "sources": ["components/main-form/components/form-slider.uvue", "components/main-form/components/form-input.uvue"], "sourcesContent": ["<template>\n\t<form-container :label=\"fieldName\" :show-error=\"showError\" :tip=\"tip\" :error-message=\"errorMessage\" :label-color=\"labelColor\"\n\t\t:background-color=\"backgroundColor\">\n\t\t<template #input-content>\n\t\t\t<view class=\"slider-container\">\n\t\t\t\t<view class=\"slider-wrapper\">\n\t\t\t\t\t<slider\n\t\t\t\t\t\t:value=\"sliderValue\"\n\t\t\t\t\t\t:min=\"minValue\"\n\t\t\t\t\t\t:max=\"maxValue\"\n\t\t\t\t\t\t:step=\"stepValue\"\n\t\t\t\t\t\t@change=\"onSliderChange\"\n\t\t\t\t\t\t:show-value=\"false\"\n\t\t\t\t\t\t:activeColor=\"activeColor\"\n\t\t\t\t\t\t:backgroundColor=\"sliderBackgroundColor\"\n\t\t\t\t\t\t:block-color=\"blockColor\"\n\t\t\t\t\t\t:block-size=\"blockSize\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"input-wrapper\">\n\t\t\t\t\t<input\n\t\t\t\t\t\tclass=\"slider-input\"\n\t\t\t\t\t\ttype=\"number\"\n\t\t\t\t\t\tv-model=\"inputValue\"\n\t\t\t\t\t\t@input=\"onInputChange\"\n\t\t\t\t\t\t@blur=\"onInputBlur\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</template>\n\t</form-container>\n</template>\n\n<script lang=\"uts\">\n\timport { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts'\n\timport FormContainer from './form-container.uvue'\n\n\texport default {\n\t\tname: \"FormSlider\",\n\t\tcomponents: {\n\t\t\tFormContainer\n\t\t},\n\t\tprops: {\n\t\t\tdata: {\n\t\t\t\ttype: null as any as PropType<FormFieldData>\n\t\t\t},\n\t\t\tindex: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\tkeyName: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"\"\n\t\t\t},\n\t\t\tlabelColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#000\"\n\t\t\t},\n\t\t\tbackgroundColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#f1f4f9\"\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tfieldName: \"\",\n\t\t\t\tfieldValue: 0,\n\t\t\t\tisSave: false,\n\t\t\t\tsave_key: \"\",\n\t\t\t\ttip: \"\",\n\t\t\t\tminValue: 0,\n\t\t\t\tmaxValue: 100,\n\t\t\t\tstepValue: 1,\n\t\t\t\tsliderValue: 0,\n\t\t\t\tinputValue: \"0\",\n\t\t\t\tactiveColor: \"#3399FF\",\n\t\t\t\tsliderBackgroundColor: \"#000000\",\n\t\t\t\tblockColor: \"#8A6DE9\",\n\t\t\t\tblockSize: 20,\n\t\t\t\tshowError: false,\n\t\t\t\terrorMessage: \"\"\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\n\t\t},\n\t\twatch: {\n\t\t\tdata: {\n\t\t\t\thandler(obj: FormFieldData) {\n\t\t\t\t\t// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue\n\t\t\t\t\tconst newValue = obj.value as number\n\t\t\t\t\tif (newValue !== this.fieldValue) {\n\t\t\t\t\t\tthis.fieldValue = newValue\n\t\t\t\t\t\tthis.updateSliderAndInput()\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t}\n\t\t},\n\t\tcreated(): void {\n\t\t\t// 初始化时调用一次即可\n\t\t\tconst fieldObj = this.$props[\"data\"] as FormFieldData\n\t\t\tthis.initFieldData(fieldObj)\n\t\t},\n\t\tmethods: {\n\t\t\t// 初始化字段数据（仅在首次加载时调用）\n\t\t\tinitFieldData(fieldObj: FormFieldData): void {\n\t\t\t\tconst fieldKey = fieldObj.key\n\t\t\t\tconst fieldValue = fieldObj.value as number\n\n\t\t\t\t// 设置基本信息\n\t\t\t\tthis.fieldName = fieldObj.name\n\t\t\t\tthis.fieldValue = fieldValue\n\t\t\t\tthis.isSave = fieldObj.isSave ?? false\n\t\t\t\tthis.save_key = this.keyName + \"_\" + fieldKey\n\n\t\t\t\t// 解析配置信息\n\t\t\t\tconst extalJson = fieldObj.extra as UTSJSONObject\n\t\t\t\tthis.minValue = extalJson.getNumber(\"min\") ?? 0\n\t\t\t\tthis.maxValue = extalJson.getNumber(\"max\") ?? 100\n\t\t\t\tthis.stepValue = extalJson.getNumber(\"step\") ?? 1\n\t\t\t\tthis.tip = extalJson.getString(\"tip\") ?? \"\"\n\t\t\t\tthis.activeColor = extalJson.getString(\"activeColor\") ?? \"#3399FF\"\n\t\t\t\tthis.sliderBackgroundColor = extalJson.getString(\"sliderBackgroundColor\") ?? \"#000000\"\n\t\t\t\tthis.blockColor = extalJson.getString(\"blockColor\") ?? \"#8A6DE9\"\n\t\t\t\tthis.blockSize = extalJson.getNumber(\"blockSize\") ?? 20\n\n\t\t\t\t// 更新滑块和输入框的值\n\t\t\t\tthis.updateSliderAndInput()\n\n\t\t\t\t// 获取缓存\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.getCache()\n\t\t\t\t}, 500)\n\t\t\t},\n\n\t\t\t// 更新滑块和输入框的值\n\t\t\tupdateSliderAndInput(): void {\n\t\t\t\tthis.sliderValue = this.fieldValue\n\t\t\t\tthis.inputValue = this.fieldValue.toString()\n\t\t\t},\n\n\t\t\t// 验证值是否在有效范围内\n\t\t\tvalidateValue(value: number): number {\n\t\t\t\tif (value < this.minValue) {\n\t\t\t\t\treturn this.minValue\n\t\t\t\t}\n\t\t\t\tif (value > this.maxValue) {\n\t\t\t\t\treturn this.maxValue\n\t\t\t\t}\n\n\t\t\t\t// 处理步长\n\t\t\t\tif (this.stepValue % 1 == 0) {\n\t\t\t\t\t// 整数步长\n\t\t\t\t\treturn Math.round(value)\n\t\t\t\t} else {\n\t\t\t\t\t// 小数步长，保留一位小数\n\t\t\t\t\treturn Number.from(value.toFixed(1))\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tgetCache(): void {\n\t\t\t\tif (this.isSave) {\n\t\t\t\t\tconst that = this\n\t\t\t\t\tuni.getStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tsuccess: (res: GetStorageSuccess) => {\n\t\t\t\t\t\t\tconst save_value = res.data as number\n\t\t\t\t\t\t\tconst validatedValue = that.validateValue(save_value)\n\t\t\t\t\t\t\tthat.fieldValue = validatedValue\n\t\t\t\t\t\t\tthat.updateSliderAndInput()\n\t\t\t\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\t\t\t\tindex: this.index,\n\t\t\t\t\t\t\t\tvalue: validatedValue\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthis.change(result)\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tsetCache(): void {\n\t\t\t\tif (this.isSave) {\n\t\t\t\t\tuni.setStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tdata: this.fieldValue\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tvalidate(): boolean {\n\t\t\t\t// 滑块组件通常不需要额外验证，因为值已经被限制在min-max范围内\n\t\t\t\tthis.showError = false\n\t\t\t\tthis.errorMessage = \"\"\n\t\t\t\treturn true\n\t\t\t},\n\n\t\t\tchange(event: FormChangeEvent): void {\n\t\t\t\t// 更新字段值\n\t\t\t\tthis.fieldValue = event.value as number\n\t\t\t\t// 保存缓存\n\t\t\t\tthis.setCache()\n\t\t\t\t// 触发父组件事件\n\t\t\t\tthis.$emit('change', event)\n\t\t\t},\n\n\t\t\tonSliderChange(event: UniSliderChangeEvent): void {\n\t\t\t\tconst rawValue = event.detail.value as number\n\t\t\t\tconst validatedValue = this.validateValue(rawValue)\n\n\t\t\t\tthis.inputValue = validatedValue.toString()\n\n\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\tindex: this.index,\n\t\t\t\t\tvalue: validatedValue\n\t\t\t\t}\n\t\t\t\tthis.change(result)\n\t\t\t},\n\n\t\t\tonInputChange(event: UniInputEvent): void {\n\t\t\t\tconst inputStr = event.detail.value as string\n\t\t\t\tconst inputNum = parseFloat(inputStr)\n\n\t\t\t\tif (!isNaN(inputNum)) {\n\t\t\t\t\tconst validatedValue = this.validateValue(inputNum)\n\t\t\t\t\tthis.sliderValue = validatedValue\n\n\t\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\t\tindex: this.index,\n\t\t\t\t\t\tvalue: validatedValue\n\t\t\t\t\t}\n\t\t\t\t\tthis.change(result)\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tonInputBlur(): void {\n\t\t\t\t// 在失去焦点时进行验证和格式化\n\t\t\t\tconst inputNum = parseFloat(this.inputValue)\n\t\t\t\tif (isNaN(inputNum)) {\n\t\t\t\t\t// 如果输入无效，恢复到当前字段值\n\t\t\t\t\tthis.inputValue = this.fieldValue.toString()\n\t\t\t\t} else {\n\t\t\t\t\t// 验证并格式化输入值\n\t\t\t\t\tconst validatedValue = this.validateValue(inputNum)\n\t\t\t\t\tthis.inputValue = validatedValue.toString()\n\t\t\t\t\tthis.sliderValue = validatedValue\n\n\t\t\t\t\tif (validatedValue !== this.fieldValue) {\n\t\t\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\t\t\tindex: this.index,\n\t\t\t\t\t\t\tvalue: validatedValue\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.change(result)\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.validate()\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.slider-container {\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t}\n\n\t.slider-wrapper {\n\t\tflex: 1;\n\t\tmargin-right: 20rpx;\n\t}\n\n\t.input-wrapper {\n\t\twidth: 120rpx;\n\t}\n\n\t.slider-input {\n\t\twidth: 100%;\n\t\theight: 60rpx;\n\t\ttext-align: center;\n\t\tborder: 1rpx solid #ddd;\n\t\tborder-radius: 10rpx;\n\t\tpadding: 0 10rpx;\n\t\tbox-sizing: border-box;\n\t}\n</style>", null], "names": [], "mappings": ";;;;;;;;;;;;;+BA6HS;+BAoBJ;AA5GC;;kBA8DJ,OAAW,IAAG,CAAA;YAEb,IAAM,WAAW,IAAI,CAAC,QAAM,CAAC,OAAM,CAAA,EAAA;YACnC,IAAI,CAAC,aAAa,CAAC;QACpB;;;;;UAfE,IAAQ,kBAAkB,EAAA;YAEzB,IAAM,WAAW,IAAI,KAAI,CAAA,EAAA,CAAK,MAAK;YACnC,IAAI,aAAa,IAAI,CAAC,UAAU,EAAE;gBACjC,IAAI,CAAC,UAAS,GAAI;gBAClB,IAAI,CAAC,oBAAoB;;QAE3B;uBACA,OAAM,IAAG;;;;;;;;eA/FZ,IA6BiB,2BAAA,IA7BA,WAAO,KAAA,SAAS,EAAG,gBAAY,KAAA,SAAS,EAAG,SAAK,KAAA,GAAG,EAAG,mBAAe,KAAA,YAAY,EAAG,iBAAa,KAAA,UAAU,EAC1H,sBAAkB,KAAA,eAAe,OACvB,mBAAa,YACvB,gBAwBO,GAAA;mBAAA;gBAxBP,IAwBO,QAAA,IAxBD,WAAM,qBAAkB;oBAC7B,IAaO,QAAA,IAbD,WAAM,mBAAgB;wBAC3B,IAWE,mBAAA,IAVA,WAAO,KAAA,WAAW,EAClB,SAAK,KAAA,QAAQ,EACb,SAAK,KAAA,QAAQ,EACb,UAAM,KAAA,SAAS,EACf,cAAQ,KAAA,cAAc,EACtB,gBAAY,KAAK,EACjB,iBAAa,KAAA,WAAW,EACxB,qBAAiB,KAAA,qBAAqB,EACtC,iBAAa,KAAA,UAAU,EACvB,gBAAY,KAAA,SAAS;;;;;;;;;;;;oBAGxB,IAQO,QAAA,IARD,WAAM,kBAAe;wBAC1B,IAME,SAAA,IALD,WAAM,gBACN,UAAK,0BACI,KAAA,UAAU;;gCAAV,KAAA,UAAU,GAAA,SAAA,MAAA,CAAA,KAAA;4BAAA;;4BACX,KAAA,aAAa;yBAAA,EACpB,YAAM,KAAA,WAAW;;;;;;;;;;;;;;;;;;;;;;;aAwCpB;aACA;aACA;aACA;aACA;aACA;aACA;aACA;aACA;aACA;aACA;aACA;aACA;aACA;aACA;aACA;;;mBAfA,eAAW,IACX,gBAAY,CAAC,EACb,YAAQ,KAAK,EACb,cAAU,IACV,SAAK,IACL,cAAU,CAAC,EACX,cAAU,GAAG,EACb,eAAW,CAAC,EACZ,iBAAa,CAAC,EACd,gBAAY,KACZ,iBAAa,WACb,2BAAuB,WACvB,gBAAY,WACZ,eAAW,EAAE,EACb,eAAW,KAAK,EAChB,kBAAc;;aA0Bf;aAAA,qBAAc,uBAAuB,GAAG,IAAG,CAAA;QAC1C,IAAM,WAAW,SAAS,GAAE;QAC5B,IAAM,aAAa,SAAS,KAAI,CAAA,EAAA,CAAK,MAAK;QAG1C,IAAI,CAAC,SAAQ,GAAI,SAAS,IAAG;QAC7B,IAAI,CAAC,UAAS,GAAI;QAClB,IAAI,CAAC,MAAK,GAAI,SAAS,MAAK,IAAK,KAAI;QACrC,IAAI,CAAC,QAAO,GAAI,IAAI,CAAC,OAAM,GAAI,MAAM;QAGrC,IAAM,YAAY,SAAS,KAAI,CAAA,EAAA,CAAK;QACpC,IAAI,CAAC,QAAO,GAAI,UAAU,SAAS,CAAC,UAAU,CAAA;QAC9C,IAAI,CAAC,QAAO,GAAI,UAAU,SAAS,CAAC,UAAU,GAAE;QAChD,IAAI,CAAC,SAAQ,GAAI,UAAU,SAAS,CAAC,WAAW,CAAA;QAChD,IAAI,CAAC,GAAE,GAAI,UAAU,SAAS,CAAC,UAAU;QACzC,IAAI,CAAC,WAAU,GAAI,UAAU,SAAS,CAAC,kBAAkB;QACzD,IAAI,CAAC,qBAAoB,GAAI,UAAU,SAAS,CAAC,4BAA4B;QAC7E,IAAI,CAAC,UAAS,GAAI,UAAU,SAAS,CAAC,iBAAiB;QACvD,IAAI,CAAC,SAAQ,GAAI,UAAU,SAAS,CAAC,gBAAgB,EAAC;QAGtD,IAAI,CAAC,oBAAoB;QAGzB,WAAW,KAAI;YACd,IAAI,CAAC,QAAQ;QACd;UAAG,GAAG;IACP;aAGA;aAAA,+BAAwB,IAAG,CAAA;QAC1B,IAAI,CAAC,WAAU,GAAI,IAAI,CAAC,UAAS;QACjC,IAAI,CAAC,UAAS,GAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAA,EAAA;IAC3C;aAGA;aAAA,qBAAc,OAAO,MAAM,GAAG,MAAK,CAAA;QAClC,IAAI,QAAQ,IAAI,CAAC,QAAQ,EAAE;YAC1B,OAAO,IAAI,CAAC,QAAO;;QAEpB,IAAI,QAAQ,IAAI,CAAC,QAAQ,EAAE;YAC1B,OAAO,IAAI,CAAC,QAAO;;QAIpB,IAAI,IAAI,CAAC,SAAQ,GAAI,CAAA,IAAK,CAAC,EAAE;YAE5B,OAAO,KAAK,KAAK,CAAC;eACZ;YAEN,OAAO,UAAO,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC;;IAEpC;aAEA;aAAA,mBAAY,IAAG,CAAA;QACd,IAAI,IAAI,CAAC,MAAM,EAAE;YAChB,IAAM,OAAO,IAAG;YAChB,iCACC,MAAK,IAAI,CAAC,QAAQ,EAClB,UAAS,IAAC,KAAK,kBAAoB;gBAClC,IAAM,aAAa,IAAI,IAAG,CAAA,EAAA,CAAK,MAAK;gBACpC,IAAM,iBAAiB,KAAK,aAAa,CAAC;gBAC1C,KAAK,UAAS,GAAI;gBAClB,KAAK,oBAAoB;gBACzB,IAAM,yBACL,QAAO,IAAI,CAAC,KAAK,EACjB,QAAO;gBAER,IAAI,CAAC,MAAM,CAAC;YACb;;;IAGH;aAEA;aAAA,mBAAY,IAAG,CAAA;QACd,IAAI,IAAI,CAAC,MAAM,EAAE;YAChB,iCACC,MAAK,IAAI,CAAC,QAAQ,EAClB,OAAM,IAAI,CAAC,UAAS;;IAGvB;aAEA;aAAA,mBAAY,OAAM,CAAA;QAEjB,IAAI,CAAC,SAAQ,GAAI,KAAI;QACrB,IAAI,CAAC,YAAW,GAAI;QACpB,OAAO,IAAG;IACX;aAEA;aAAA,cAAO,sBAAsB,GAAG,IAAG,CAAA;QAElC,IAAI,CAAC,UAAS,GAAI,MAAM,KAAI,CAAA,EAAA,CAAK,MAAK;QAEtC,IAAI,CAAC,QAAQ;QAEb,IAAI,CAAC,OAAK,CAAC,UAAU;IACtB;aAEA;aAAA,sBAAe,OAAO,oBAAoB,GAAG,IAAG,CAAA;QAC/C,IAAM,WAAW,MAAM,MAAM,CAAC,KAAI,CAAA,EAAA,CAAK,MAAK;QAC5C,IAAM,iBAAiB,IAAI,CAAC,aAAa,CAAC;QAE1C,IAAI,CAAC,UAAS,GAAI,eAAe,QAAQ,CAAA,EAAA;QAEzC,IAAM,yBACL,QAAO,IAAI,CAAC,KAAK,EACjB,QAAO;QAER,IAAI,CAAC,MAAM,CAAC;IACb;aAEA;aAAA,qBAAc,OAAO,aAAa,GAAG,IAAG,CAAA;QACvC,IAAM,WAAW,MAAM,MAAM,CAAC,KAAI,CAAA,EAAA,CAAK,MAAK;QAC5C,IAAM,WAAW,WAAW;QAE5B,IAAI,CAAC,MAAM,WAAW;YACrB,IAAM,iBAAiB,IAAI,CAAC,aAAa,CAAC;YAC1C,IAAI,CAAC,WAAU,GAAI;YAEnB,IAAM,yBACL,QAAO,IAAI,CAAC,KAAK,EACjB,QAAO;YAER,IAAI,CAAC,MAAM,CAAC;;IAEd;aAEA;aAAA,sBAAe,IAAG,CAAA;QAEjB,IAAM,WAAW,WAAW,IAAI,CAAC,UAAU;QAC3C,IAAI,MAAM,WAAW;YAEpB,IAAI,CAAC,UAAS,GAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAA,EAAA;eACpC;YAEN,IAAM,iBAAiB,IAAI,CAAC,aAAa,CAAC;YAC1C,IAAI,CAAC,UAAS,GAAI,eAAe,QAAQ,CAAA,EAAA;YACzC,IAAI,CAAC,WAAU,GAAI;YAEnB,IAAI,mBAAmB,IAAI,CAAC,UAAU,EAAE;gBACvC,IAAM,yBACL,QAAO,IAAI,CAAC,KAAK,EACjB,QAAO;gBAER,IAAI,CAAC,MAAM,CAAC;;;QAGd,IAAI,CAAC,QAAQ;IACd;;mBA1NK;;;;;;;;;;;;;6FAUK,CAAA,qDAIA,0DAIA,mEAIA;;;;;;;;;AAsMZ"}