@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNIC178CB1
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import io.dcloud.uniapp.extapi.getStorage as uni_getStorage
import io.dcloud.uniapp.extapi.setStorageSync as uni_setStorageSync
open class GenComponentsMainFormComponentsFormSelect : VueComponent {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {
        onCreated(fun(): Unit {
            this.initializeValue()
        }
        , __ins)
        this.`$watch`(fun(): Any? {
            return this.data
        }
        , fun(newValue: FormFieldData2): Unit {
            this.updateSelectedValue(newValue.value)
        }
        , WatchOptions(deep = true, immediate = true))
    }
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        return _cE("view", _uM("class" to "form-select-container"), _uA(
            _cE("view", _uM("class" to "form-select-label", "style" to _nS(_uM("color" to _ctx.color))), _uA(
                _cE("text", null, _tD(_ctx.data.name), 1)
            ), 4),
            _cE("view", _uM("class" to "form-select-box", "style" to _nS(_uM("backgroundColor" to _ctx.bgColor)), "onClick" to _ctx.showPicker), _uA(
                _cE("text", _uM("class" to _nC(_uA(
                    "form-select-text",
                    _uM("placeholder" to (_ctx.selectedText == ""))
                ))), _tD(_ctx.selectedText || _ctx.data.tip || "请选择"), 3),
                _cE("text", _uM("class" to "form-select-arrow"), "▼")
            ), 12, _uA(
                "onClick"
            )),
            if (isTrue(_ctx.showModal)) {
                _cE("view", _uM("key" to 0, "class" to "form-select-modal", "onClick" to _ctx.hidePicker), _uA(
                    _cE("view", _uM("class" to "form-select-popup", "onClick" to _ctx.stopPropagation), _uA(
                        _cE("view", _uM("class" to "form-select-header"), _uA(
                            _cE("text", _uM("class" to "form-select-cancel", "onClick" to _ctx.hidePicker), "取消", 8, _uA(
                                "onClick"
                            )),
                            _cE("text", _uM("class" to "form-select-title"), _tD(_ctx.data.name), 1),
                            _cE("text", _uM("class" to "form-select-confirm", "onClick" to _ctx.confirmSelection), "确定", 8, _uA(
                                "onClick"
                            ))
                        )),
                        _cE("view", _uM("class" to "form-select-list"), _uA(
                            _cE(Fragment, null, RenderHelpers.renderList(_ctx.options, fun(item, index, __index, _cached): Any {
                                return _cE("view", _uM("class" to _nC(_uA(
                                    "form-select-item",
                                    _uM("selected" to (item.value == _ctx.tempSelectedValue))
                                )), "key" to index, "onClick" to fun(){
                                    _ctx.selectItem(item)
                                }), _uA(
                                    _cE("text", null, _tD(item.label), 1),
                                    if (item.value == _ctx.tempSelectedValue) {
                                        _cE("text", _uM("key" to 0, "class" to "form-select-check"), "✓")
                                    } else {
                                        _cC("v-if", true)
                                    }
                                ), 10, _uA(
                                    "onClick"
                                ))
                            }), 128)
                        ))
                    ), 8, _uA(
                        "onClick"
                    ))
                ), 8, _uA(
                    "onClick"
                ))
            } else {
                _cC("v-if", true)
            }
        ))
    }
    open var data: FormFieldData2 by `$props`
    open var index: Number by `$props`
    open var color: String by `$props`
    open var bgColor: String by `$props`
    open var keyName: String by `$props`
    open var selectedValue: Any by `$data`
    open var selectedText: String by `$data`
    open var showModal: Boolean by `$data`
    open var tempSelectedValue: Any by `$data`
    open var saveKey: String by `$data`
    open var options: UTSArray<SelectOption> by `$data`
    @Suppress("USELESS_CAST")
    override fun data(): Map<String, Any?> {
        return _uM("selectedValue" to null as Any, "selectedText" to "", "showModal" to false, "tempSelectedValue" to null as Any, "saveKey" to "", "options" to computed<UTSArray<SelectOption>>(fun(): UTSArray<SelectOption> {
            return this.data.options || _uA()
        }
        ))
    }
    open var initializeValue = ::gen_initializeValue_fn
    open fun gen_initializeValue_fn(): Unit {
        if (this.data.isSave == true && this.keyName != "") {
            this.saveKey = this.keyName + "_" + this.data.key
            uni_getStorage(GetStorageOptions(key = this.saveKey, success = fun(res): Unit {
                this.updateSelectedValue(res.data)
                this.emitChange(res.data)
            }, fail = fun(_): Unit {
                this.updateSelectedValue(this.data.value)
            }))
        } else {
            this.updateSelectedValue(this.data.value)
        }
    }
    open var updateSelectedValue = ::gen_updateSelectedValue_fn
    open fun gen_updateSelectedValue_fn(value: Any): Unit {
        this.selectedValue = value
        this.tempSelectedValue = value
        val option = this.options.find(fun(item: SelectOption): Boolean {
            return item.value == value
        }
        )
        this.selectedText = if (option != null) {
            option.label
        } else {
            ""
        }
    }
    open var showPicker = ::gen_showPicker_fn
    open fun gen_showPicker_fn(): Unit {
        this.tempSelectedValue = this.selectedValue
        this.showModal = true
    }
    open var hidePicker = ::gen_hidePicker_fn
    open fun gen_hidePicker_fn(): Unit {
        this.showModal = false
    }
    open var stopPropagation = ::gen_stopPropagation_fn
    open fun gen_stopPropagation_fn(): Unit {}
    open var selectItem = ::gen_selectItem_fn
    open fun gen_selectItem_fn(item: SelectOption): Unit {
        this.tempSelectedValue = item.value
    }
    open var confirmSelection = ::gen_confirmSelection_fn
    open fun gen_confirmSelection_fn(): Unit {
        this.selectedValue = this.tempSelectedValue
        val option = this.options.find(fun(item: SelectOption): Boolean {
            return item.value == this.tempSelectedValue
        }
        )
        this.selectedText = if (option != null) {
            option.label
        } else {
            ""
        }
        this.emitChange(this.selectedValue)
        this.saveValue(this.selectedValue)
        this.hidePicker()
    }
    open var emitChange = ::gen_emitChange_fn
    open fun gen_emitChange_fn(value: Any): Unit {
        this.`$emit`("change", _uO("index" to this.index, "value" to value))
    }
    open var saveValue = ::gen_saveValue_fn
    open fun gen_saveValue_fn(value: Any): Unit {
        if (this.data.isSave == true && this.saveKey != "") {
            try {
                uni_setStorageSync(this.saveKey, value)
            }
             catch (e: Throwable) {
                console.error("保存数据失败:", e, " at components/main-form/components/form-select.uvue:197")
            }
        }
    }
    companion object {
        var name = "FormSelect"
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("form-select-container" to _pS(_uM("width" to "100%", "display" to "flex", "flexDirection" to "column", "paddingTop" to 0, "paddingRight" to "20rpx", "paddingBottom" to 0, "paddingLeft" to "20rpx", "marginBottom" to "20rpx")), "form-select-label" to _pS(_uM("width" to "100%", "marginBottom" to "10rpx", "fontSize" to "32rpx")), "form-select-box" to _pS(_uM("width" to "100%", "height" to "100rpx", "borderTopWidth" to "1rpx", "borderRightWidth" to "1rpx", "borderBottomWidth" to "1rpx", "borderLeftWidth" to "1rpx", "borderTopStyle" to "solid", "borderRightStyle" to "solid", "borderBottomStyle" to "solid", "borderLeftStyle" to "solid", "borderTopColor" to "#e0e0e0", "borderRightColor" to "#e0e0e0", "borderBottomColor" to "#e0e0e0", "borderLeftColor" to "#e0e0e0", "boxSizing" to "border-box", "paddingTop" to 0, "paddingRight" to "20rpx", "paddingBottom" to 0, "paddingLeft" to "20rpx", "borderTopLeftRadius" to "20rpx", "borderTopRightRadius" to "20rpx", "borderBottomRightRadius" to "20rpx", "borderBottomLeftRadius" to "20rpx", "display" to "flex", "flexDirection" to "row", "alignItems" to "center", "justifyContent" to "space-between")), "form-select-text" to _uM("" to _uM("flex" to 1, "fontSize" to "30rpx", "color" to "#333333"), ".placeholder" to _uM("color" to "#999999")), "form-select-arrow" to _pS(_uM("fontSize" to "24rpx", "color" to "#666666")), "form-select-modal" to _pS(_uM("position" to "fixed", "top" to 0, "left" to 0, "right" to 0, "bottom" to 0, "backgroundColor" to "rgba(0,0,0,0.5)", "display" to "flex", "justifyContent" to "center", "alignItems" to "flex-end", "zIndex" to 1000)), "form-select-popup" to _pS(_uM("width" to "100%", "maxHeight" to "800rpx", "backgroundColor" to "#ffffff", "borderTopLeftRadius" to "20rpx", "borderTopRightRadius" to "20rpx", "borderBottomRightRadius" to 0, "borderBottomLeftRadius" to 0, "display" to "flex", "flexDirection" to "column")), "form-select-header" to _pS(_uM("height" to "100rpx", "display" to "flex", "flexDirection" to "row", "alignItems" to "center", "justifyContent" to "space-between", "paddingTop" to 0, "paddingRight" to "30rpx", "paddingBottom" to 0, "paddingLeft" to "30rpx", "borderBottomWidth" to "1rpx", "borderBottomStyle" to "solid", "borderBottomColor" to "#e0e0e0")), "form-select-cancel" to _pS(_uM("fontSize" to "32rpx", "color" to "#1F6ED4")), "form-select-confirm" to _pS(_uM("fontSize" to "32rpx", "color" to "#1F6ED4")), "form-select-title" to _pS(_uM("fontSize" to "34rpx", "fontWeight" to "bold", "color" to "#333333")), "form-select-list" to _pS(_uM("flex" to 1)), "form-select-item" to _uM("" to _uM("height" to "100rpx", "display" to "flex", "flexDirection" to "row", "alignItems" to "center", "justifyContent" to "space-between", "paddingTop" to 0, "paddingRight" to "30rpx", "paddingBottom" to 0, "paddingLeft" to "30rpx", "borderBottomWidth" to "1rpx", "borderBottomStyle" to "solid", "borderBottomColor" to "#f0f0f0", "fontSize" to "32rpx", "color" to "#333333"), ".selected" to _uM("backgroundColor" to "#f8f9fa")), "form-select-check" to _pS(_uM("color" to "#1F6ED4", "fontWeight" to "bold")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = _uM()
        var emits: Map<String, Any?> = _uM()
        var props = _nP(_uM("data" to _uM("type" to "Object", "default" to fun(): FormFieldData2 {
            return (FormFieldData2(key = "", name = "", type = "select", value = "", options = _uA()))
        }
        ), "index" to _uM("type" to "Number", "default" to 0), "color" to _uM("type" to "String", "default" to "#333333"), "bgColor" to _uM("type" to "String", "default" to "#f8f9fa"), "keyName" to _uM("type" to "String", "default" to "")))
        var propsNeedCastKeys = _uA(
            "data",
            "index",
            "color",
            "bgColor",
            "keyName"
        )
        var components: Map<String, CreateVueComponent> = _uM()
    }
}
