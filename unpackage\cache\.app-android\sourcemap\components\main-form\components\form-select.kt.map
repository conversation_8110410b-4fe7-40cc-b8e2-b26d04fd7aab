{"version": 3, "sources": ["components/main-form/components/form-select.uvue", "components/main-form/components/form-input.uvue"], "sourcesContent": ["<template>\n\t<view class=\"form-select-container\">\n\t\t<view class=\"form-select-label\" :style=\"{ color: color }\">\n\t\t\t<text>{{ data.name }}</text>\n\t\t</view>\n\n\t\t<view class=\"form-select-box\" :style=\"{ backgroundColor: bgColor }\" @click=\"showPicker\">\n\t\t\t<text class=\"form-select-text\" :class=\"{ 'placeholder': selectedText == '' }\">\n\t\t\t\t{{ selectedText || data.tip || '请选择' }}\n\t\t\t</text>\n\t\t\t<text class=\"form-select-arrow\">▼</text>\n\t\t</view>\n\n\t\t<!-- 选择器弹窗 -->\n\t\t<view class=\"form-select-modal\" v-if=\"showModal\" @click=\"hidePicker\">\n\t\t\t<view class=\"form-select-popup\" @click=\"stopPropagation\">\n\t\t\t\t<view class=\"form-select-header\">\n\t\t\t\t\t<text class=\"form-select-cancel\" @click=\"hidePicker\">取消</text>\n\t\t\t\t\t<text class=\"form-select-title\">{{ data.name }}</text>\n\t\t\t\t\t<text class=\"form-select-confirm\" @click=\"confirmSelection\">确定</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"form-select-list\">\n\t\t\t\t\t<view \n\t\t\t\t\t\tclass=\"form-select-item\" \n\t\t\t\t\t\t:class=\"{ 'selected': item.value == tempSelectedValue }\"\n\t\t\t\t\t\tv-for=\"(item, index) in options\" \n\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t@click=\"selectItem(item)\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<text>{{ item.label }}</text>\n\t\t\t\t\t\t<text class=\"form-select-check\" v-if=\"item.value == tempSelectedValue\">✓</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script lang=\"uts\">\n\t// 选项类型\n\ttype SelectOption = {\n\t\tlabel: string\n\t\tvalue: any\n\t}\n\n\t// 表单项数据类型\n\ttype FormFieldData = {\n\t\tkey: string\n\t\tname: string\n\t\ttype: string\n\t\tvalue: any\n\t\ttip?: string\n\t\toptions?: Array<SelectOption>\n\t\tisSave?: boolean\n\t}\n\n\texport default {\n\t\tname: \"FormSelect\",\n\t\tprops: {\n\t\t\tdata: {\n\t\t\t\ttype: Object as PropType<FormFieldData>,\n\t\t\t\tdefault: (): FormFieldData => ({\n\t\t\t\t\tkey: \"\",\n\t\t\t\t\tname: \"\",\n\t\t\t\t\ttype: \"select\",\n\t\t\t\t\tvalue: \"\",\n\t\t\t\t\toptions: []\n\t\t\t\t})\n\t\t\t},\n\t\t\tindex: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\tcolor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#333333\"\n\t\t\t},\n\t\t\tbgColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#f8f9fa\"\n\t\t\t},\n\t\t\tkeyName: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"\"\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tselectedValue: null as any,\n\t\t\t\tselectedText: \"\",\n\t\t\t\tshowModal: false,\n\t\t\t\ttempSelectedValue: null as any,\n\t\t\t\tsaveKey: \"\"\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\toptions(): Array<SelectOption> {\n\t\t\t\treturn this.data.options || []\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tdata: {\n\t\t\t\thandler(newValue: FormFieldData): void {\n\t\t\t\t\tthis.updateSelectedValue(newValue.value)\n\t\t\t\t},\n\t\t\t\tdeep: true,\n\t\t\t\timmediate: true\n\t\t\t}\n\t\t},\n\t\tcreated(): void {\n\t\t\tthis.initializeValue()\n\t\t},\n\t\tmethods: {\n\t\t\t// 初始化值\n\t\t\tinitializeValue(): void {\n\t\t\t\tif (this.data.isSave == true && this.keyName != \"\") {\n\t\t\t\t\tthis.saveKey = this.keyName + \"_\" + this.data.key\n\t\t\t\t\t\n\t\t\t\t\t// 从存储中获取值\n\t\t\t\t\tuni.getStorage({\n\t\t\t\t\t\tkey: this.saveKey,\n\t\t\t\t\t\tsuccess: (res): void => {\n\t\t\t\t\t\t\tthis.updateSelectedValue(res.data)\n\t\t\t\t\t\t\tthis.emitChange(res.data)\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (): void => {\n\t\t\t\t\t\t\tthis.updateSelectedValue(this.data.value)\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t} else {\n\t\t\t\t\tthis.updateSelectedValue(this.data.value)\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 更新选中值\n\t\t\tupdateSelectedValue(value: any): void {\n\t\t\t\tthis.selectedValue = value\n\t\t\t\tthis.tempSelectedValue = value\n\t\t\t\t\n\t\t\t\t// 查找对应的文本\n\t\t\t\tconst option = this.options.find((item: SelectOption): boolean => {\n\t\t\t\t\treturn item.value == value\n\t\t\t\t})\n\t\t\t\tthis.selectedText = option != null ? option.label : \"\"\n\t\t\t},\n\n\t\t\t// 显示选择器\n\t\t\tshowPicker(): void {\n\t\t\t\tthis.tempSelectedValue = this.selectedValue\n\t\t\t\tthis.showModal = true\n\t\t\t},\n\n\t\t\t// 隐藏选择器\n\t\t\thidePicker(): void {\n\t\t\t\tthis.showModal = false\n\t\t\t},\n\n\t\t\t// 阻止事件冒泡\n\t\t\tstopPropagation(): void {\n\t\t\t\t// 阻止点击事件冒泡到父元素\n\t\t\t},\n\n\t\t\t// 选择项目\n\t\t\tselectItem(item: SelectOption): void {\n\t\t\t\tthis.tempSelectedValue = item.value\n\t\t\t},\n\n\t\t\t// 确认选择\n\t\t\tconfirmSelection(): void {\n\t\t\t\tthis.selectedValue = this.tempSelectedValue\n\t\t\t\t\n\t\t\t\t// 更新显示文本\n\t\t\t\tconst option = this.options.find((item: SelectOption): boolean => {\n\t\t\t\t\treturn item.value == this.tempSelectedValue\n\t\t\t\t})\n\t\t\t\tthis.selectedText = option != null ? option.label : \"\"\n\n\t\t\t\tthis.emitChange(this.selectedValue)\n\t\t\t\tthis.saveValue(this.selectedValue)\n\t\t\t\tthis.hidePicker()\n\t\t\t},\n\n\t\t\t// 发送变更事件\n\t\t\temitChange(value: any): void {\n\t\t\t\tthis.$emit(\"change\", {\n\t\t\t\t\tindex: this.index,\n\t\t\t\t\tvalue: value\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 保存值到本地存储\n\t\t\tsaveValue(value: any): void {\n\t\t\t\tif (this.data.isSave == true && this.saveKey != \"\") {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tuni.setStorageSync(this.saveKey, value)\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.error(\"保存数据失败:\", e)\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.form-select-container {\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tpadding: 0 20rpx;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.form-select-label {\n\t\twidth: 100%;\n\t\tmargin-bottom: 10rpx;\n\t\tfont-size: 32rpx;\n\t}\n\n\t.form-select-box {\n\t\twidth: 100%;\n\t\theight: 100rpx;\n\t\tborder: 1rpx solid #e0e0e0;\n\t\tbox-sizing: border-box;\n\t\tpadding: 0 20rpx;\n\t\tborder-radius: 20rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t}\n\n\t.form-select-text {\n\t\tflex: 1;\n\t\tfont-size: 30rpx;\n\t\tcolor: #333333;\n\t}\n\n\t.form-select-text.placeholder {\n\t\tcolor: #999999;\n\t}\n\n\t.form-select-arrow {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666666;\n\t}\n\n\t.form-select-modal {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: flex-end;\n\t\tz-index: 1000;\n\t}\n\n\t.form-select-popup {\n\t\twidth: 100%;\n\t\tmax-height: 800rpx;\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 20rpx 20rpx 0 0;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\t.form-select-header {\n\t\theight: 100rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 0 30rpx;\n\t\tborder-bottom: 1rpx solid #e0e0e0;\n\t}\n\n\t.form-select-cancel,\n\t.form-select-confirm {\n\t\tfont-size: 32rpx;\n\t\tcolor: #1F6ED4;\n\t}\n\n\t.form-select-title {\n\t\tfont-size: 34rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t}\n\n\t.form-select-list {\n\t\tflex: 1;\n\t}\n\n\t.form-select-item {\n\t\theight: 100rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 0 30rpx;\n\t\tborder-bottom: 1rpx solid #f0f0f0;\n\t\tfont-size: 32rpx;\n\t\tcolor: #333333;\n\t}\n\n\t.form-select-item.selected {\n\t\tbackground-color: #f8f9fa;\n\t}\n\n\t.form-select-check {\n\t\tcolor: #1F6ED4;\n\t\tfont-weight: bold;\n\t}\n</style>\n", null], "names": [], "mappings": ";;;;;;;;;;;;;+BAsHK;+BAhHS;AAkDR;;kBAqDJ,OAAW,IAAG,CAAA;YACb,IAAI,CAAC,eAAe;QACrB;;;;;UATE,IAAQ,UAAU,cAAa,GAAG,IAAG,CAAA;YACpC,IAAI,CAAC,mBAAmB,CAAC,SAAS,KAAK;QACxC;uBACA,OAAM,IAAI,EACV,YAAW,IAAG;;;;;;eAzGjB,IAkCO,QAAA,IAlCD,WAAM,0BAAuB;YAClC,IAEO,QAAA,IAFD,WAAM,qBAAqB,WAAK,IAAE,IAAA,WAAA,KAAA,KAAA;gBACvC,IAA4B,QAAA,IAAA,EAAA,IAAnB,KAAA,IAAI,CAAC,IAAI,GAAA,CAAA;;YAGnB,IAKO,QAAA,IALD,WAAM,mBAAmB,WAAK,IAAE,IAAA,qBAAA,KAAA,OAAA,IAA+B,aAAO,KAAA,UAAU;gBACrF,IAEO,QAAA,IAFD,WAAK,IAAA;oBAAC;oBAA2B,IAAA,kBAAA,KAAA,YAAA,IAAA;iBAAqC,QACxE,KAAA,YAAY,IAAI,KAAA,IAAI,CAAC,GAAG,IAAA,QAAA,CAAA;gBAE5B,IAAwC,QAAA,IAAlC,WAAM,sBAAoB;;;;uBAIK,KAAA,SAAS;gBAA/C,IAoBO,QAAA,gBApBD,WAAM,qBAAsC,aAAO,KAAA,UAAU;oBAClE,IAkBO,QAAA,IAlBD,WAAM,qBAAqB,aAAO,KAAA,eAAe;wBACtD,IAIO,QAAA,IAJD,WAAM,uBAAoB;4BAC/B,IAA8D,QAAA,IAAxD,WAAM,sBAAsB,aAAO,KAAA,UAAU,GAAE,MAAE,CAAA,EAAA;gCAAA;6BAAA;4BACvD,IAAsD,QAAA,IAAhD,WAAM,sBAAmB,IAAI,KAAA,IAAI,CAAC,IAAI,GAAA,CAAA;4BAC5C,IAAqE,QAAA,IAA/D,WAAM,uBAAuB,aAAO,KAAA,gBAAgB,GAAE,MAAE,CAAA,EAAA;gCAAA;6BAAA;;wBAE/D,IAWO,QAAA,IAXD,WAAM,qBAAkB;4BAC7B,IASO,UAAA,IAAA,EAAA,cAAA,UAAA,CANkB,KAAA,OAAO,EAAA,IAAvB,MAAM,OAAN,SAAI,UAAA,GAAA,CAAA;uCAHb,IASO,QAAA,IARN,WAAK,IAAA;oCAAC;oCACE,IAAA,eAAA,KAAA,KAAA,IAAA,KAAA,iBAAA;iCAA+C,GAEtD,SAAK,OACL,aAAK,KAAA;oCAAE,KAAA,UAAU,CAAC;gCAAI;oCAEvB,IAA6B,QAAA,IAAA,EAAA,IAApB,KAAK,KAAK,GAAA,CAAA;oCACmB,IAAA,KAAK,KAAK,IAAI,KAAA,iBAAiB;wCAArE,IAA+E,QAAA,gBAAzE,WAAM,sBAA2D;;;;;;;;;;;;;;;;;;;;mBA8BhD;;;;;aA4BzB,eAAuB,GAAG;aAC1B;aACA;aACA,mBAA2B,GAAG;aAC9B;sBAIU,SAAM;;;mBARhB,mBAAe,IAAG,CAAA,EAAA,CAAK,GAAG,EAC1B,kBAAc,IACd,eAAW,KAAK,EAChB,uBAAmB,IAAG,CAAA,EAAA,CAAK,GAAG,EAC9B,aAAS,0BAIC,SAAM,eAAjB,OAAW,SAAM,cAAY;YAC5B,OAAO,IAAI,CAAC,IAAI,CAAC,OAAM,IAAK,KAAC;QAC9B;;;aAgBA;aAAA,0BAAmB,IAAG,CAAA;QACrB,IAAI,IAAI,CAAC,IAAI,CAAC,MAAK,IAAK,IAAG,IAAK,IAAI,CAAC,OAAM,IAAK,IAAI;YACnD,IAAI,CAAC,OAAM,GAAI,IAAI,CAAC,OAAM,GAAI,MAAM,IAAI,CAAC,IAAI,CAAC,GAAE;YAGhD,iCACC,MAAK,IAAI,CAAC,OAAO,EACjB,UAAS,IAAC,MAAM,IAAG,CAAG;gBACrB,IAAI,CAAC,mBAAmB,CAAC,IAAI,IAAI;gBACjC,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI;YACzB,GACA,OAAM,QAAI,IAAG,CAAG;gBACf,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK;YACzC;eAEK;YACN,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK;;IAE1C;aAGA;aAAA,2BAAoB,OAAO,GAAG,GAAG,IAAG,CAAA;QACnC,IAAI,CAAC,aAAY,GAAI;QACrB,IAAI,CAAC,iBAAgB,GAAI;QAGzB,IAAM,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAC,MAAM,eAAe,OAAM,CAAG;YAC/D,OAAO,KAAK,KAAI,IAAK;QACtB;;QACA,IAAI,CAAC,YAAW,GAAI,IAAA,UAAU,IAAG;YAAI,OAAO,KAAI;;YAAI;;IACrD;aAGA;aAAA,qBAAc,IAAG,CAAA;QAChB,IAAI,CAAC,iBAAgB,GAAI,IAAI,CAAC,aAAY;QAC1C,IAAI,CAAC,SAAQ,GAAI,IAAG;IACrB;aAGA;aAAA,qBAAc,IAAG,CAAA;QAChB,IAAI,CAAC,SAAQ,GAAI,KAAI;IACtB;aAGA;aAAA,0BAAmB,IAAG,CAAA,CAEtB;aAGA;aAAA,kBAAW,MAAM,YAAY,GAAG,IAAG,CAAA;QAClC,IAAI,CAAC,iBAAgB,GAAI,KAAK,KAAI;IACnC;aAGA;aAAA,2BAAoB,IAAG,CAAA;QACtB,IAAI,CAAC,aAAY,GAAI,IAAI,CAAC,iBAAgB;QAG1C,IAAM,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAC,MAAM,eAAe,OAAM,CAAG;YAC/D,OAAO,KAAK,KAAI,IAAK,IAAI,CAAC,iBAAgB;QAC3C;;QACA,IAAI,CAAC,YAAW,GAAI,IAAA,UAAU,IAAG;YAAI,OAAO,KAAI;;YAAI;;QAEpD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa;QAClC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa;QACjC,IAAI,CAAC,UAAU;IAChB;aAGA;aAAA,kBAAW,OAAO,GAAG,GAAG,IAAG,CAAA;QAC1B,IAAI,CAAC,OAAK,CAAC,UAAU,IACpB,WAAO,IAAI,CAAC,KAAK,EACjB,WAAO;IAET;aAGA;aAAA,iBAAU,OAAO,GAAG,GAAG,IAAG,CAAA;QACzB,IAAI,IAAI,CAAC,IAAI,CAAC,MAAK,IAAK,IAAG,IAAK,IAAI,CAAC,OAAM,IAAK,IAAI;YACnD,IAAI;gBACH,mBAAmB,IAAI,CAAC,OAAO,EAAE;;aAChC,OAAO,cAAG;gBACX,QAAQ,KAAK,CAAC,WAAW,GAAC;;;IAG7B;;mBA9IK;;;;;;;;;;;;;2EAIK,OAAI;mBAAiB,gBAC7B,MAAK,IACL,OAAM,IACN,OAAM,UACN,QAAO,IACP,UAAS,KAAC,CACV;;2DAIQ,CAAA,mDAIA,8DAIA,8DAIA;;;;;;;;;;AAsHZ"}