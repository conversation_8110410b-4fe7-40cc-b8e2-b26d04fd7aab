<template>
	<scroll-view  class="content">
		<button @click="openColorPicker">选择颜色</button>
		<button @click="openFun">对话框</button>
		<main-color-picker ref="colorPicker" @cancel="onCancel" @confirm="onConfirm"></main-color-picker>

		
		<main-form 
				:formData="formConfig"
				title="用户信息表单"
				keyName="user_form"
				ref="mainForm"
			/>
		<button @click="viewForm">查看表单1</button>
	</scroll-view>
</template>

<script>
	import { FormFieldData } from '@/components/main-form/form_type.uts'
	export default {
		data(){
			return {
			    formConfig: [
					{
						key: "username",
						name: "用户名1",
						type: "input",
						value: "",
						isSave:true,
						extra:{
							minLength: 0,
							maxLength: 20,
							placeholder: "请输入用户名",
							tip:"123",
							inputmode: "digit" 
						}
					},
					{
						key: "password",
						name: "密码",
						type: "input",
						value: "",
						isSave:false,
						extra:{
							minLength: 6,
							maxLength: 20,
							placeholder: "请输入密码",
							tip:"密码请自己保管好",
							inputmode: "number"
						}
					},
					{
						key: "email",
						name: "邮箱地址",
						type: "textarea",
						value: "",
						isSave:true,
						extra:{
							minLength: 6,
							maxLength: 20,
							placeholder: "请输入密码",
							tip:""
						}
						
					},
					{
					    key: "enable_feature",
					    name: "启用功能",
					    type: "switch",
					    value: 1,
					    isSave: false,
					    extra: { 
					        "varType": "number",
					        "tip": "开启后将启用此功能"
					    }
					},
					{
					    key: "slider",
					    name: "slider测试",
					    type: "slider",
					    value: 10,
					    isSave: true,
					    extra: {
					        "min": 0,
					        "max": 100,
							"step":1
					    }
					},
					{
					    key: "numberbox",
					    name: "数量选择",
					    type: "numberbox",
					    value: 5,
					    isSave: true,
					    extra: {
					        "min": 1,
					        "max": 50,
							"step": 1,
							"unit": "个",
							"tip": "请选择数量"
					    }
					}
				] as FormFieldData[]  
			}
		},
		methods: {
			viewForm(){
				// this.formConfig[0].value="111"
				console.log(this.formConfig)
			},
			openFun() {
				uni.showModal({
					title: "onLoad 调用示例,请手动取消",
					editable: true,
					content: "Hello World",
					success: function (res) {
						if (res.confirm) {
							console.log('用户点击确定')
						} else if (res.cancel) {
							console.log('用户点击取消')
						}
					}
				})
			},
			openColorPicker() {
				// 使用$callMethod方式调用组件方法
				(this.$refs['colorPicker'] as ComponentPublicInstance).$callMethod('open')
			},

			onCancel() {
				console.log('用户取消选择')
			},

			onConfirm(result : UTSJSONObject) {
				console.log(result)
				console.log('选择的颜色:', result['color'])
				console.log('RGBA值:', result['rgba'])
			}
		}
	}
</script>
<style>
	.content {
		height: 100%;
		padding: 20rpx;
	}
</style>