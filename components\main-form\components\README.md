# 表单组件使用说明

## 概述

基于 `Minput.vue` 组件的设计理念，创建了一个通用的表单容器组件 `form-container.uvue`，该组件提供了统一的表单项布局和样式，只有输入框部分可以自定义。

## 组件结构

### FormContainer (form-container.uvue)
通用表单容器组件，提供：
- 标签显示
- 错误状态显示
- 统一的样式和布局
- 可自定义的输入内容插槽

#### Props
- `label`: 标签文本
- `showError`: 是否显示错误状态
- `errorMessage`: 错误信息
- `labelColor`: 标签颜色 (默认: "#000")
- `backgroundColor`: 背景颜色 (默认: "#f1f4f9")

#### 插槽
- `input-content`: 输入内容插槽，可以放置任何输入组件

## 已实现的表单组件

### 1. FormInput (form-input.uvue)
基础文本输入组件
```vue
<form-input 
  :data="inputData" 
  :index="0" 
  @change="handleChange" 
  @blur="handleBlur" />
```

### 2. FormTextarea (form-textarea.uvue)
多行文本输入组件
```vue
<form-textarea 
  :data="textareaData" 
  :index="1" 
  @change="handleChange" 
  @blur="handleBlur" />
```

### 3. FormNumberWithUnit (form-number-with-unit.uvue)
带单位的数字输入组件
```vue
<form-number-with-unit 
  :data="numberData" 
  :index="2" 
  @change="handleChange" 
  @blur="handleBlur" />
```

## 数据格式

### 基础输入组件数据格式
```javascript
const inputData = {
  key: "username",
  name: "用户名",
  type: "text",
  value: ""
}
```

### 多行文本组件数据格式
```javascript
const textareaData = {
  key: "description",
  name: "描述",
  type: "textarea",
  value: "",
  maxLength: 200
}
```

### 数字带单位组件数据格式
```javascript
const numberData = {
  key: "weight",
  name: "重量",
  type: "number",
  value: "",
  unit: "kg",
  min: 0,
  max: 1000,
  placeholder: "输入重量"
}
```

## 事件

所有组件都支持以下事件：

### change
输入内容变化时触发
```javascript
{
  index: 0,        // 组件索引
  value: "输入值"   // 当前输入值
}
```

### blur
输入框失焦时触发
```javascript
{
  index: 0,        // 组件索引
  value: "输入值"   // 当前输入值
}
```

## 自定义新的表单组件

要创建新的表单组件，只需：

1. 导入 `FormContainer` 组件
2. 在 `input-content` 插槽中放置自定义的输入元素
3. 实现必要的事件处理逻辑

示例：
```vue
<template>
  <form-container
    :label="fieldName"
    :show-error="showError"
    :error-message="errorMessage"
    :label-color="labelColor"
    :background-color="backgroundColor">
    <template #input-content>
      <!-- 自定义输入内容 -->
      <your-custom-input />
    </template>
  </form-container>
</template>

<script lang="uts">
import FormContainer from './form-container.uvue'

export default {
  name: "YourCustomFormComponent",
  components: {
    FormContainer
  },
  // ... 其他配置
}
</script>
```

## 样式说明

- 容器组件提供了统一的布局和基础样式
- 支持阴影效果 (qShadow1 类)
- 错误状态会显示红色边框
- 输入框自动占满可用空间 (flex: 1)
- 响应式设计，使用 rpx 单位

## 注意事项

1. 所有组件都使用 UTS 语言编写，类型要求严格
2. 事件参数类型需要明确定义
3. 避免使用隐式类型转换
4. 遵循 uni-app x 的开发规范
